import { useEffect, useRef } from 'react';
import { useLocation } from '@tanstack/react-router';

interface NavigationHistory {
  getPreviousLocation: () => string;
}

export function useNavigationHistory(): NavigationHistory {
  const location = useLocation();
  const previousLocationRef = useRef<string>('/pages');

  useEffect(() => {
    // Don't update previous location if we're on the profile page
    // This prevents the profile page from becoming the "previous" location
    if (location.pathname !== '/profile') {
      previousLocationRef.current = location.pathname;
    }
  }, [location.pathname]);

  const getPreviousLocation = () => {
    const previous = previousLocationRef.current;
    
    // If previous location is root or login/signup, default to /pages
    if (previous === '/' || previous === '/login' || previous === '/signup') {
      return '/pages';
    }
    
    return previous;
  };

  return {
    getPreviousLocation,
  };
}
